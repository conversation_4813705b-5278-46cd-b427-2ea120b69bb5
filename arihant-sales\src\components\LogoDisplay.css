.logo-display {
  display: inline-block;
  position: relative;
}

.logo-container {
  position: relative;
  display: inline-block;
  border-radius: 50%;
  overflow: hidden;
}

.trendy-logo {
  display: block;
  border-radius: 50%;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  z-index: 2;
}

/* Size variations */
.logo-display.small .trendy-logo {
  width: 40px;
  height: 40px;
}

.logo-display.medium .trendy-logo {
  width: 60px;
  height: 60px;
}

.logo-display.large .trendy-logo {
  width: 120px;
  height: 120px;
}

.logo-display.extra-large .trendy-logo {
  width: 200px;
  height: 200px;
}

/* Gradient border effect */
.logo-container::before {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  background: linear-gradient(45deg, #ED3637, #F79E62, #ED3637, #F79E62);
  border-radius: 50%;
  z-index: 1;
  animation: rotate 3s linear infinite;
}

.logo-container::after {
  content: '';
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  background: white;
  border-radius: 50%;
  z-index: 1;
}

/* Overlay effects */
.logo-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  z-index: 3;
  pointer-events: none;
}

.logo-shine {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.3) 50%, transparent 70%);
  transform: rotate(45deg);
  transition: all 0.6s ease;
  opacity: 0;
}

/* Hover effects */
.logo-display:hover .trendy-logo {
  transform: scale(1.1) rotate(5deg);
  filter: brightness(1.1) contrast(1.1);
}

.logo-display:hover .logo-shine {
  opacity: 1;
  animation: shine 0.6s ease-in-out;
}

.logo-display:hover .logo-container::before {
  animation-duration: 1s;
}

/* Glow effect */
.logo-display.glow .logo-container {
  box-shadow: 0 0 20px rgba(237, 54, 55, 0.4),
              0 0 40px rgba(237, 54, 55, 0.2),
              0 0 60px rgba(237, 54, 55, 0.1);
}

.logo-display.glow:hover .logo-container {
  box-shadow: 0 0 30px rgba(237, 54, 55, 0.6),
              0 0 60px rgba(237, 54, 55, 0.4),
              0 0 90px rgba(237, 54, 55, 0.2);
}

/* Animated version */
.logo-display.animated .trendy-logo {
  animation: pulse 2s ease-in-out infinite;
}

.logo-display.animated .logo-container::before {
  animation: rotate 4s linear infinite, pulse-border 2s ease-in-out infinite;
}

/* Keyframe animations */
@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes shine {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
    opacity: 0;
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes pulse-border {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Responsive adjustments */
@media screen and (max-width: 768px) {
  .logo-display.large .trendy-logo {
    width: 100px;
    height: 100px;
  }
  
  .logo-display.extra-large .trendy-logo {
    width: 150px;
    height: 150px;
  }
}

@media screen and (max-width: 480px) {
  .logo-display.large .trendy-logo {
    width: 80px;
    height: 80px;
  }
  
  .logo-display.extra-large .trendy-logo {
    width: 120px;
    height: 120px;
  }
}
