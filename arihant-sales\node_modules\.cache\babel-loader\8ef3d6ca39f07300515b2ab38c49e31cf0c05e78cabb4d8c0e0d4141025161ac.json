{"ast": null, "code": "var _jsxFileName = \"C:\\\\arihant_sales\\\\arihant-sales\\\\src\\\\components\\\\LogoDisplay.js\";\nimport React from 'react';\nimport './LogoDisplay.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LogoDisplay = ({\n  size = 'medium',\n  showGlow = false,\n  animated = false\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `logo-display ${size} ${showGlow ? 'glow' : ''} ${animated ? 'animated' : ''}`,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"logo-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n        src: \"/logo.jpg\",\n        alt: \"Arihant Sales\",\n        className: \"trendy-logo\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"logo-overlay\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"logo-shine\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 10,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = LogoDisplay;\nexport default LogoDisplay;\nvar _c;\n$RefreshReg$(_c, \"LogoDisplay\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "LogoDisplay", "size", "showGlow", "animated", "className", "children", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/arihant_sales/arihant-sales/src/components/LogoDisplay.js"], "sourcesContent": ["import React from 'react';\nimport './LogoDisplay.css';\n\nconst LogoDisplay = ({ size = 'medium', showGlow = false, animated = false }) => {\n  return (\n    <div className={`logo-display ${size} ${showGlow ? 'glow' : ''} ${animated ? 'animated' : ''}`}>\n      <div className=\"logo-container\">\n        <img src=\"/logo.jpg\" alt=\"Arihant Sales\" className=\"trendy-logo\" />\n        <div className=\"logo-overlay\">\n          <div className=\"logo-shine\"></div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default LogoDisplay;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAMC,WAAW,GAAGA,CAAC;EAAEC,IAAI,GAAG,QAAQ;EAAEC,QAAQ,GAAG,KAAK;EAAEC,QAAQ,GAAG;AAAM,CAAC,KAAK;EAC/E,oBACEJ,OAAA;IAAKK,SAAS,EAAE,gBAAgBH,IAAI,IAAIC,QAAQ,GAAG,MAAM,GAAG,EAAE,IAAIC,QAAQ,GAAG,UAAU,GAAG,EAAE,EAAG;IAAAE,QAAA,eAC7FN,OAAA;MAAKK,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BN,OAAA;QAAKO,GAAG,EAAC,WAAW;QAACC,GAAG,EAAC,eAAe;QAACH,SAAS,EAAC;MAAa;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACnEZ,OAAA;QAAKK,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC3BN,OAAA;UAAKK,SAAS,EAAC;QAAY;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACC,EAAA,GAXIZ,WAAW;AAajB,eAAeA,WAAW;AAAC,IAAAY,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}