{"ast": null, "code": "var _jsxFileName = \"C:\\\\arihant_sales\\\\arihant-sales\\\\src\\\\pages\\\\About.js\";\nimport React from 'react';\nimport './About.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst About = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"about\",\n    children: [/*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"about-hero\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"about-hero-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            children: [\"About \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"gradient-text\",\n              children: \"Arihant Sales\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 11,\n              columnNumber: 23\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 11,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Discover our journey, mission, and commitment to excellence\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 12,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 10,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"company-story\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"story-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"story-text\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"Our Story\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 22,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Arihant Sales was founded with a vision to provide exceptional products and services to our customers. Since our inception, we have been committed to maintaining the highest standards of quality and customer satisfaction.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 23,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Our journey began with a simple belief: that every customer deserves the best. This philosophy has guided us through years of growth and success, helping us build lasting relationships with clients across various industries.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 28,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Today, we stand as a trusted name in the market, known for our reliability, innovation, and unwavering commitment to excellence. Our team of dedicated professionals works tirelessly to ensure that we continue to exceed expectations and deliver value to our customers.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 33,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"story-image\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"image-placeholder\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Company Story\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 41,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 40,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"mission-vision\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mv-grid\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mv-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mv-icon\",\n              children: /*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"icon-mission\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 54,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 53,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Our Mission\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 56,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"To provide exceptional products and services that exceed customer expectations while maintaining the highest standards of quality, integrity, and professionalism in all our business operations.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 57,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mv-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mv-icon\",\n              children: /*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"icon-vision\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 65,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Our Vision\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"To be the leading provider in our industry, recognized for innovation, quality, and customer satisfaction. We aim to create lasting value for our stakeholders and contribute positively to the communities we serve.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 68,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"values\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"section-title\",\n          children: \"Our Core Values\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"values-grid\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"value-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"value-icon\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Q\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 85,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 84,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Quality\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 87,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"We never compromise on quality and ensure every product meets the highest standards.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"value-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"value-icon\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"I\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 92,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Integrity\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"We conduct business with honesty, transparency, and ethical practices.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"value-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"value-icon\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"I\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 99,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Innovation\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"We continuously seek new ways to improve and provide better solutions.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"value-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"value-icon\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 106,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Customer Focus\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Our customers are at the heart of everything we do.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"value-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"value-icon\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"R\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 113,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Reliability\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"We deliver on our promises and build trust through consistent performance.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"value-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"value-icon\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"E\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 120,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Excellence\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"We strive for excellence in every aspect of our business operations.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"team\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"section-title\",\n          children: \"Our Team\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"team-description\",\n          children: \"Our success is driven by our talented and dedicated team of professionals who bring years of experience and expertise to serve our customers better.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"team-stats\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"50+\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Team Members\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"10+\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Years Experience\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"1000+\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Happy Customers\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"24/7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Customer Support\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 130,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = About;\nexport default About;\nvar _c;\n$RefreshReg$(_c, \"About\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "About", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/arihant_sales/arihant-sales/src/pages/About.js"], "sourcesContent": ["import React from 'react';\nimport './About.css';\n\nconst About = () => {\n  return (\n    <div className=\"about\">\n      {/* Hero Section */}\n      <section className=\"about-hero\">\n        <div className=\"container\">\n          <div className=\"about-hero-content\">\n            <h1>About <span className=\"gradient-text\">Arihant Sales</span></h1>\n            <p>Discover our journey, mission, and commitment to excellence</p>\n          </div>\n        </div>\n      </section>\n\n      {/* Company Story */}\n      <section className=\"company-story\">\n        <div className=\"container\">\n          <div className=\"story-content\">\n            <div className=\"story-text\">\n              <h2>Our Story</h2>\n              <p>\n                Arihant Sales was founded with a vision to provide exceptional products and services \n                to our customers. Since our inception, we have been committed to maintaining the highest \n                standards of quality and customer satisfaction.\n              </p>\n              <p>\n                Our journey began with a simple belief: that every customer deserves the best. This \n                philosophy has guided us through years of growth and success, helping us build lasting \n                relationships with clients across various industries.\n              </p>\n              <p>\n                Today, we stand as a trusted name in the market, known for our reliability, innovation, \n                and unwavering commitment to excellence. Our team of dedicated professionals works \n                tirelessly to ensure that we continue to exceed expectations and deliver value to our customers.\n              </p>\n            </div>\n            <div className=\"story-image\">\n              <div className=\"image-placeholder\">\n                <span>Company Story</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Mission & Vision */}\n      <section className=\"mission-vision\">\n        <div className=\"container\">\n          <div className=\"mv-grid\">\n            <div className=\"mv-card\">\n              <div className=\"mv-icon\">\n                <i className=\"icon-mission\"></i>\n              </div>\n              <h3>Our Mission</h3>\n              <p>\n                To provide exceptional products and services that exceed customer expectations \n                while maintaining the highest standards of quality, integrity, and professionalism \n                in all our business operations.\n              </p>\n            </div>\n            <div className=\"mv-card\">\n              <div className=\"mv-icon\">\n                <i className=\"icon-vision\"></i>\n              </div>\n              <h3>Our Vision</h3>\n              <p>\n                To be the leading provider in our industry, recognized for innovation, quality, \n                and customer satisfaction. We aim to create lasting value for our stakeholders \n                and contribute positively to the communities we serve.\n              </p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Values */}\n      <section className=\"values\">\n        <div className=\"container\">\n          <h2 className=\"section-title\">Our Core Values</h2>\n          <div className=\"values-grid\">\n            <div className=\"value-card\">\n              <div className=\"value-icon\">\n                <span>Q</span>\n              </div>\n              <h4>Quality</h4>\n              <p>We never compromise on quality and ensure every product meets the highest standards.</p>\n            </div>\n            <div className=\"value-card\">\n              <div className=\"value-icon\">\n                <span>I</span>\n              </div>\n              <h4>Integrity</h4>\n              <p>We conduct business with honesty, transparency, and ethical practices.</p>\n            </div>\n            <div className=\"value-card\">\n              <div className=\"value-icon\">\n                <span>I</span>\n              </div>\n              <h4>Innovation</h4>\n              <p>We continuously seek new ways to improve and provide better solutions.</p>\n            </div>\n            <div className=\"value-card\">\n              <div className=\"value-icon\">\n                <span>C</span>\n              </div>\n              <h4>Customer Focus</h4>\n              <p>Our customers are at the heart of everything we do.</p>\n            </div>\n            <div className=\"value-card\">\n              <div className=\"value-icon\">\n                <span>R</span>\n              </div>\n              <h4>Reliability</h4>\n              <p>We deliver on our promises and build trust through consistent performance.</p>\n            </div>\n            <div className=\"value-card\">\n              <div className=\"value-icon\">\n                <span>E</span>\n              </div>\n              <h4>Excellence</h4>\n              <p>We strive for excellence in every aspect of our business operations.</p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Team Section */}\n      <section className=\"team\">\n        <div className=\"container\">\n          <h2 className=\"section-title\">Our Team</h2>\n          <p className=\"team-description\">\n            Our success is driven by our talented and dedicated team of professionals who bring \n            years of experience and expertise to serve our customers better.\n          </p>\n          <div className=\"team-stats\">\n            <div className=\"stat-card\">\n              <h3>50+</h3>\n              <p>Team Members</p>\n            </div>\n            <div className=\"stat-card\">\n              <h3>10+</h3>\n              <p>Years Experience</p>\n            </div>\n            <div className=\"stat-card\">\n              <h3>1000+</h3>\n              <p>Happy Customers</p>\n            </div>\n            <div className=\"stat-card\">\n              <h3>24/7</h3>\n              <p>Customer Support</p>\n            </div>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n};\n\nexport default About;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErB,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAClB,oBACED,OAAA;IAAKE,SAAS,EAAC,OAAO;IAAAC,QAAA,gBAEpBH,OAAA;MAASE,SAAS,EAAC,YAAY;MAAAC,QAAA,eAC7BH,OAAA;QAAKE,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBH,OAAA;UAAKE,SAAS,EAAC,oBAAoB;UAAAC,QAAA,gBACjCH,OAAA;YAAAG,QAAA,GAAI,QAAM,eAAAH,OAAA;cAAME,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnEP,OAAA;YAAAG,QAAA,EAAG;UAA2D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVP,OAAA;MAASE,SAAS,EAAC,eAAe;MAAAC,QAAA,eAChCH,OAAA;QAAKE,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBH,OAAA;UAAKE,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BH,OAAA;YAAKE,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBH,OAAA;cAAAG,QAAA,EAAI;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClBP,OAAA;cAAAG,QAAA,EAAG;YAIH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJP,OAAA;cAAAG,QAAA,EAAG;YAIH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJP,OAAA;cAAAG,QAAA,EAAG;YAIH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACNP,OAAA;YAAKE,SAAS,EAAC,aAAa;YAAAC,QAAA,eAC1BH,OAAA;cAAKE,SAAS,EAAC,mBAAmB;cAAAC,QAAA,eAChCH,OAAA;gBAAAG,QAAA,EAAM;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVP,OAAA;MAASE,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eACjCH,OAAA;QAAKE,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBH,OAAA;UAAKE,SAAS,EAAC,SAAS;UAAAC,QAAA,gBACtBH,OAAA;YAAKE,SAAS,EAAC,SAAS;YAAAC,QAAA,gBACtBH,OAAA;cAAKE,SAAS,EAAC,SAAS;cAAAC,QAAA,eACtBH,OAAA;gBAAGE,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,eACNP,OAAA;cAAAG,QAAA,EAAI;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpBP,OAAA;cAAAG,QAAA,EAAG;YAIH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACNP,OAAA;YAAKE,SAAS,EAAC,SAAS;YAAAC,QAAA,gBACtBH,OAAA;cAAKE,SAAS,EAAC,SAAS;cAAAC,QAAA,eACtBH,OAAA;gBAAGE,SAAS,EAAC;cAAa;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,eACNP,OAAA;cAAAG,QAAA,EAAI;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnBP,OAAA;cAAAG,QAAA,EAAG;YAIH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVP,OAAA;MAASE,SAAS,EAAC,QAAQ;MAAAC,QAAA,eACzBH,OAAA;QAAKE,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBH,OAAA;UAAIE,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClDP,OAAA;UAAKE,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BH,OAAA;YAAKE,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBH,OAAA;cAAKE,SAAS,EAAC,YAAY;cAAAC,QAAA,eACzBH,OAAA;gBAAAG,QAAA,EAAM;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC,eACNP,OAAA;cAAAG,QAAA,EAAI;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChBP,OAAA;cAAAG,QAAA,EAAG;YAAoF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxF,CAAC,eACNP,OAAA;YAAKE,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBH,OAAA;cAAKE,SAAS,EAAC,YAAY;cAAAC,QAAA,eACzBH,OAAA;gBAAAG,QAAA,EAAM;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC,eACNP,OAAA;cAAAG,QAAA,EAAI;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClBP,OAAA;cAAAG,QAAA,EAAG;YAAsE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1E,CAAC,eACNP,OAAA;YAAKE,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBH,OAAA;cAAKE,SAAS,EAAC,YAAY;cAAAC,QAAA,eACzBH,OAAA;gBAAAG,QAAA,EAAM;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC,eACNP,OAAA;cAAAG,QAAA,EAAI;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnBP,OAAA;cAAAG,QAAA,EAAG;YAAsE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1E,CAAC,eACNP,OAAA;YAAKE,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBH,OAAA;cAAKE,SAAS,EAAC,YAAY;cAAAC,QAAA,eACzBH,OAAA;gBAAAG,QAAA,EAAM;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC,eACNP,OAAA;cAAAG,QAAA,EAAI;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvBP,OAAA;cAAAG,QAAA,EAAG;YAAmD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC,eACNP,OAAA;YAAKE,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBH,OAAA;cAAKE,SAAS,EAAC,YAAY;cAAAC,QAAA,eACzBH,OAAA;gBAAAG,QAAA,EAAM;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC,eACNP,OAAA;cAAAG,QAAA,EAAI;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpBP,OAAA;cAAAG,QAAA,EAAG;YAA0E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9E,CAAC,eACNP,OAAA;YAAKE,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBH,OAAA;cAAKE,SAAS,EAAC,YAAY;cAAAC,QAAA,eACzBH,OAAA;gBAAAG,QAAA,EAAM;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC,eACNP,OAAA;cAAAG,QAAA,EAAI;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnBP,OAAA;cAAAG,QAAA,EAAG;YAAoE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVP,OAAA;MAASE,SAAS,EAAC,MAAM;MAAAC,QAAA,eACvBH,OAAA;QAAKE,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBH,OAAA;UAAIE,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3CP,OAAA;UAAGE,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAAC;QAGhC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJP,OAAA;UAAKE,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBH,OAAA;YAAKE,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBH,OAAA;cAAAG,QAAA,EAAI;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACZP,OAAA;cAAAG,QAAA,EAAG;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eACNP,OAAA;YAAKE,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBH,OAAA;cAAAG,QAAA,EAAI;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACZP,OAAA;cAAAG,QAAA,EAAG;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,eACNP,OAAA;YAAKE,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBH,OAAA;cAAAG,QAAA,EAAI;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACdP,OAAA;cAAAG,QAAA,EAAG;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACNP,OAAA;YAAKE,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBH,OAAA;cAAAG,QAAA,EAAI;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACbP,OAAA;cAAAG,QAAA,EAAG;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACC,EAAA,GA3JIP,KAAK;AA6JX,eAAeA,KAAK;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}