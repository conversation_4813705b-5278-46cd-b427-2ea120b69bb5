.navbar {
  background: #ffffff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
  padding: 0.5rem 0;
}

.navbar-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.navbar-brand {
  display: flex;
  align-items: center;
  position: relative;
}

.navbar-brand a {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: #333;
  padding: 0.5rem;
  border-radius: 15px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.navbar-brand a::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(237, 54, 55, 0.1), transparent);
  transition: left 0.5s ease;
}

.navbar-brand a:hover::before {
  left: 100%;
}

.navbar-brand a:hover {
  background: rgba(237, 54, 55, 0.05);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(237, 54, 55, 0.1);
}

/* Logo styling is now handled by LogoDisplay component */
.logo-display {
  margin-right: 1rem;
}

.navbar-title {
  font-size: 1.8rem;
  font-weight: 800;
  background: linear-gradient(45deg, #ED3637, #F79E62, #ED3637);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
  letter-spacing: -0.5px;
  transition: all 0.3s ease;
}

.navbar-title::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(45deg, #ED3637, #F79E62);
  transition: width 0.3s ease;
}

.navbar-brand:hover .navbar-title::after {
  width: 100%;
}

.navbar-brand:hover .navbar-title {
  transform: translateY(-1px);
  text-shadow: 0 2px 8px rgba(237, 54, 55, 0.3);
}

.navbar-menu {
  display: flex;
  align-items: center;
}

.navbar-nav {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 2rem;
}

.nav-item {
  position: relative;
}

.nav-link {
  text-decoration: none;
  color: #333;
  font-weight: 500;
  font-size: 1.1rem;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  position: relative;
}

.nav-link:hover {
  background: linear-gradient(45deg, #ED3637, #F79E62);
  color: white;
  transform: translateY(-2px);
}

.navbar-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
  padding: 0.5rem;
}

.bar {
  width: 25px;
  height: 3px;
  background-color: #333;
  margin: 3px 0;
  transition: 0.3s;
  border-radius: 2px;
}

/* Mobile Responsive */
@media screen and (max-width: 768px) {
  .navbar-toggle {
    display: flex;
  }

  .navbar-menu {
    position: fixed;
    left: -100%;
    top: 70px;
    flex-direction: column;
    background-color: white;
    width: 100%;
    text-align: center;
    transition: 0.3s;
    box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
    padding: 2rem 0;
  }

  .navbar-menu.active {
    left: 0;
  }

  .navbar-nav {
    flex-direction: column;
    gap: 1rem;
  }

  .nav-link {
    padding: 1rem;
    display: block;
    width: 100%;
  }

  .navbar-toggle.active .bar:nth-child(2) {
    opacity: 0;
  }

  .navbar-toggle.active .bar:nth-child(1) {
    transform: translateY(8px) rotate(45deg);
  }

  .navbar-toggle.active .bar:nth-child(3) {
    transform: translateY(-8px) rotate(-45deg);
  }
}

@media screen and (max-width: 480px) {
  .navbar-title {
    font-size: 1.4rem;
  }

  .logo-display {
    margin-right: 0.5rem;
  }

  .navbar-brand a {
    padding: 0.3rem;
  }
}
