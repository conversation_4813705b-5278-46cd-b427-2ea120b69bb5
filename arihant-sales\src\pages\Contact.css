.contact {
  min-height: 100vh;
}

/* Contact Hero Section */
.contact-hero {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 6rem 0 4rem;
  text-align: center;
}

.contact-hero-content h1 {
  font-size: 3.5rem;
  font-weight: bold;
  margin-bottom: 1rem;
  color: #333;
}

.gradient-text {
  background: linear-gradient(45deg, #ED3637, #F79E62);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.contact-hero-content p {
  font-size: 1.3rem;
  color: #666;
  max-width: 600px;
  margin: 0 auto;
}

/* Contact Content */
.contact-content {
  padding: 5rem 0;
  background: white;
}

.contact-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
}

/* Contact Information */
.contact-info h2 {
  font-size: 2.5rem;
  font-weight: bold;
  margin-bottom: 1.5rem;
  color: #333;
}

.contact-info > p {
  font-size: 1.1rem;
  color: #666;
  line-height: 1.6;
  margin-bottom: 3rem;
}

.contact-details {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.contact-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.contact-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(45deg, #ED3637, #F79E62);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
  flex-shrink: 0;
}

.contact-text h4 {
  font-size: 1.2rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
  color: #333;
}

.contact-text p {
  color: #666;
  line-height: 1.6;
}

/* Contact Form */
.contact-form {
  background: #f8f9fa;
  padding: 3rem;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.contact-form h2 {
  font-size: 2.5rem;
  font-weight: bold;
  margin-bottom: 2rem;
  color: #333;
  text-align: center;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 1rem;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: white;
  box-sizing: border-box;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #ED3637;
  box-shadow: 0 0 0 3px rgba(237, 54, 55, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

.btn {
  padding: 1rem 2rem;
  border: none;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  text-align: center;
  width: 100%;
}

.btn-primary {
  background: linear-gradient(45deg, #ED3637, #F79E62);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(237, 54, 55, 0.3);
}

/* Map Section */
.map-section {
  padding: 5rem 0;
  background: #f8f9fa;
}

.section-title {
  text-align: center;
  font-size: 2.5rem;
  font-weight: bold;
  margin-bottom: 3rem;
  color: #333;
}

.map-placeholder {
  background: white;
  height: 400px;
  border-radius: 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.map-placeholder p {
  font-size: 1.2rem;
  color: #666;
  margin: 0.5rem 0;
}

.map-placeholder p:first-child {
  font-weight: bold;
  color: #333;
}

/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Responsive Design */
@media screen and (max-width: 768px) {
  .contact-hero-content h1 {
    font-size: 2.5rem;
  }

  .contact-grid {
    grid-template-columns: 1fr;
    gap: 3rem;
  }

  .contact-form {
    padding: 2rem;
  }

  .contact-info h2,
  .contact-form h2 {
    font-size: 2rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .contact-item {
    flex-direction: column;
    text-align: center;
    align-items: center;
  }

  .contact-icon {
    margin-bottom: 1rem;
  }
}

@media screen and (max-width: 480px) {
  .contact-hero-content h1 {
    font-size: 2rem;
  }

  .contact-hero-content p {
    font-size: 1.1rem;
  }

  .contact-form {
    padding: 1.5rem;
  }

  .contact-info h2,
  .contact-form h2 {
    font-size: 1.8rem;
  }

  .section-title {
    font-size: 1.8rem;
  }

  .map-placeholder {
    height: 300px;
  }

  .map-placeholder p {
    font-size: 1rem;
  }
}
