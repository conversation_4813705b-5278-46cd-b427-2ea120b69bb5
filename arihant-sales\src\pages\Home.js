import React from 'react';
import LogoDisplay from '../components/LogoDisplay';
import './Home.css';

const Home = () => {
  return (
    <div className="home">
      {/* Hero Section */}
      <section className="hero">
        <div className="hero-container">
          <div className="hero-content">
            <h1 className="hero-title">
              Welcome to <span className="gradient-text">Arihant Sales</span>
            </h1>
            <p className="hero-subtitle">
              Your trusted partner for quality products and exceptional service since our establishment.
              We are committed to delivering excellence in every aspect of our business.
            </p>
            <div className="hero-buttons">
              <button className="btn btn-primary">Explore Products</button>
              <button className="btn btn-secondary">Learn More</button>
            </div>
          </div>
          <div className="hero-image">
            <LogoDisplay size="extra-large" showGlow={true} animated={true} />
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="features">
        <div className="container">
          <h2 className="section-title">Why Choose Arihant Sales?</h2>
          <div className="features-grid">
            <div className="feature-card">
              <div className="feature-icon">
                <i className="icon-quality"></i>
              </div>
              <h3>Quality Products</h3>
              <p>We provide only the highest quality products that meet industry standards and exceed customer expectations.</p>
            </div>
            <div className="feature-card">
              <div className="feature-icon">
                <i className="icon-service"></i>
              </div>
              <h3>Excellent Service</h3>
              <p>Our dedicated team ensures exceptional customer service and support throughout your journey with us.</p>
            </div>
            <div className="feature-card">
              <div className="feature-icon">
                <i className="icon-trust"></i>
              </div>
              <h3>Trusted Partner</h3>
              <p>With years of experience in the industry, we have built lasting relationships based on trust and reliability.</p>
            </div>
            <div className="feature-card">
              <div className="feature-icon">
                <i className="icon-innovation"></i>
              </div>
              <h3>Innovation</h3>
              <p>We continuously innovate and adapt to bring you the latest solutions and technologies in the market.</p>
            </div>
          </div>
        </div>
      </section>

      {/* About Preview Section */}
      <section className="about-preview">
        <div className="container">
          <div className="about-content">
            <div className="about-text">
              <h2>About Arihant Sales</h2>
              <p>
                Arihant Sales has been a leading name in the industry, providing exceptional products and services 
                to our valued customers. Our commitment to quality and customer satisfaction has made us a trusted 
                choice for businesses and individuals alike.
              </p>
              <p>
                We believe in building long-term relationships with our clients by delivering consistent value 
                and maintaining the highest standards of professionalism in all our interactions.
              </p>
              <button className="btn btn-primary">Learn More About Us</button>
            </div>
            <div className="about-image">
              <div className="image-placeholder">
                <span>Company Image</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="cta">
        <div className="container">
          <div className="cta-content">
            <h2>Ready to Get Started?</h2>
            <p>Contact us today to learn more about our products and services.</p>
            <button className="btn btn-primary">Contact Us Now</button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;
