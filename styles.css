/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #ffffff;
}

html {
    scroll-behavior: smooth;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Global Button Styles */
.btn {
    padding: 1rem 2rem;
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: linear-gradient(45deg, #ED3637, #F79E62);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(237, 54, 55, 0.3);
}

.btn-secondary {
    background: transparent;
    color: #ED3637;
    border: 2px solid #ED3637;
}

.btn-secondary:hover {
    background: #ED3637;
    color: white;
    transform: translateY(-2px);
}

/* Global Gradient Text */
.gradient-text {
    background: linear-gradient(45deg, #ED3637, #F79E62);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Global Section Title */
.section-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 3rem;
    color: #333;
}

/* Remove default link styles */
a {
    text-decoration: none;
    color: inherit;
}

/* Global responsive images */
img {
    max-width: 100%;
    height: auto;
}

/* Global list styles */
ul, ol {
    list-style: none;
}

/* Focus styles for accessibility */
*:focus {
    outline: 2px solid #ED3637;
    outline-offset: 2px;
}

/* Ultra Trendy Logo Display */
.logo-display {
    display: inline-block;
    position: relative;
    filter: drop-shadow(0 10px 30px rgba(237, 54, 55, 0.3));
}

.logo-container {
    position: relative;
    display: inline-block;
    background: linear-gradient(135deg, #FF6B6B, #4ECDC4, #45B7D1, #96CEB4, #FFEAA7, #DDA0DD);
    background-size: 400% 400%;
    animation: gradientShift 4s ease infinite;
    border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
    padding: 8px;
    overflow: visible;
    transform-style: preserve-3d;
}

.trendy-logo {
    display: block;
    border-radius: 25% 75% 75% 25% / 25% 25% 75% 75%;
    transition: all 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    position: relative;
    z-index: 2;
    filter: brightness(1.1) contrast(1.2) saturate(1.1);
    box-shadow: inset 0 0 20px rgba(255, 255, 255, 0.3);
}

/* Size variations with trendy proportions */
.logo-display.small .trendy-logo {
    width: 45px;
    height: 45px;
}

.logo-display .trendy-logo {
    width: 65px;
    height: 65px;
}

.logo-display.large .trendy-logo {
    width: 130px;
    height: 130px;
}

.logo-display.extra-large .trendy-logo {
    width: 220px;
    height: 220px;
}

/* Multiple animated borders */
.logo-container::before {
    content: '';
    position: absolute;
    top: -6px;
    left: -6px;
    right: -6px;
    bottom: -6px;
    background: linear-gradient(45deg, #FF6B6B, #4ECDC4, #45B7D1, #96CEB4, #FFEAA7, #DDA0DD, #FF6B6B);
    background-size: 400% 400%;
    border-radius: 35% 65% 65% 35% / 35% 35% 65% 65%;
    z-index: 0;
    animation: gradientShift 3s ease infinite reverse, morphShape 6s ease-in-out infinite;
    filter: blur(2px);
}

.logo-container::after {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    background: linear-gradient(90deg, #ED3637, #F79E62, #FF6B6B, #ED3637);
    background-size: 300% 300%;
    border-radius: 32% 68% 68% 32% / 32% 32% 68% 68%;
    z-index: 1;
    animation: gradientShift 2s ease infinite, morphShape 8s ease-in-out infinite reverse;
}

/* Floating particles effect */
.logo-overlay {
    position: absolute;
    top: -20px;
    left: -20px;
    right: -20px;
    bottom: -20px;
    z-index: 4;
    pointer-events: none;
}

.logo-overlay::before,
.logo-overlay::after {
    content: '';
    position: absolute;
    width: 6px;
    height: 6px;
    background: radial-gradient(circle, #FF6B6B, transparent);
    border-radius: 50%;
    animation: float 3s ease-in-out infinite;
}

.logo-overlay::before {
    top: 10%;
    left: 20%;
    animation-delay: 0s;
}

.logo-overlay::after {
    bottom: 15%;
    right: 25%;
    animation-delay: 1.5s;
    background: radial-gradient(circle, #4ECDC4, transparent);
}

.logo-shine {
    position: absolute;
    top: -100%;
    left: -100%;
    width: 300%;
    height: 300%;
    background: conic-gradient(from 0deg, transparent, rgba(255, 255, 255, 0.4), transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: spin 4s linear infinite;
    opacity: 0.7;
}

/* Advanced hover effects */
.logo-display:hover .trendy-logo {
    transform: scale(1.15) rotate(10deg) rotateY(15deg);
    filter: brightness(1.3) contrast(1.3) saturate(1.3) hue-rotate(15deg);
    border-radius: 20% 80% 80% 20% / 20% 20% 80% 80%;
}

.logo-display:hover .logo-container {
    transform: scale(1.05) rotateZ(5deg);
    border-radius: 25% 75% 75% 25% / 25% 25% 75% 75%;
    animation-duration: 1s;
}

.logo-display:hover .logo-shine {
    opacity: 1;
    animation-duration: 2s;
}

/* Enhanced glow effect */
.logo-display.glow .logo-container {
    box-shadow:
        0 0 30px rgba(237, 54, 55, 0.6),
        0 0 60px rgba(76, 205, 196, 0.4),
        0 0 90px rgba(69, 183, 209, 0.3),
        0 0 120px rgba(255, 107, 107, 0.2);
}

.logo-display.glow:hover .logo-container {
    box-shadow:
        0 0 40px rgba(237, 54, 55, 0.8),
        0 0 80px rgba(76, 205, 196, 0.6),
        0 0 120px rgba(69, 183, 209, 0.5),
        0 0 160px rgba(255, 107, 107, 0.4),
        0 0 200px rgba(150, 206, 180, 0.3);
}

/* Animated version with complex movements */
.logo-display.animated .trendy-logo {
    animation: complexPulse 3s ease-in-out infinite, wobble 4s ease-in-out infinite;
}

.logo-display.animated .logo-container {
    animation: gradientShift 4s ease infinite, breathe 2s ease-in-out infinite;
}

/* Advanced Keyframe Animations */
@keyframes gradientShift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

@keyframes morphShape {
    0%, 100% {
        border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
    }
    25% {
        border-radius: 70% 30% 30% 70% / 70% 70% 30% 30%;
    }
    50% {
        border-radius: 50% 50% 50% 50% / 50% 50% 50% 50%;
    }
    75% {
        border-radius: 30% 70% 30% 70% / 70% 30% 70% 30%;
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) translateX(0px);
        opacity: 0.7;
    }
    25% {
        transform: translateY(-10px) translateX(5px);
        opacity: 1;
    }
    50% {
        transform: translateY(-5px) translateX(-3px);
        opacity: 0.8;
    }
    75% {
        transform: translateY(-15px) translateX(8px);
        opacity: 0.9;
    }
}

@keyframes complexPulse {
    0%, 100% {
        transform: scale(1) rotate(0deg);
    }
    25% {
        transform: scale(1.05) rotate(2deg);
    }
    50% {
        transform: scale(1.1) rotate(0deg);
    }
    75% {
        transform: scale(1.05) rotate(-2deg);
    }
}

@keyframes wobble {
    0%, 100% {
        transform: translateX(0%);
    }
    15% {
        transform: translateX(-2px) rotate(1deg);
    }
    30% {
        transform: translateX(2px) rotate(-1deg);
    }
    45% {
        transform: translateX(-1px) rotate(0.5deg);
    }
    60% {
        transform: translateX(1px) rotate(-0.5deg);
    }
    75% {
        transform: translateX(-0.5px) rotate(0.2deg);
    }
}

@keyframes breathe {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.02);
    }
}

@keyframes glitch {
    0%, 100% {
        transform: translate(0);
        filter: hue-rotate(0deg);
    }
    10% {
        transform: translate(-2px, 2px);
        filter: hue-rotate(90deg);
    }
    20% {
        transform: translate(-2px, -2px);
        filter: hue-rotate(180deg);
    }
    30% {
        transform: translate(2px, 2px);
        filter: hue-rotate(270deg);
    }
    40% {
        transform: translate(2px, -2px);
        filter: hue-rotate(360deg);
    }
    50% {
        transform: translate(-2px, 2px);
        filter: hue-rotate(45deg);
    }
    60% {
        transform: translate(-2px, -2px);
        filter: hue-rotate(135deg);
    }
    70% {
        transform: translate(2px, 2px);
        filter: hue-rotate(225deg);
    }
    80% {
        transform: translate(2px, -2px);
        filter: hue-rotate(315deg);
    }
    90% {
        transform: translate(-2px, 2px);
        filter: hue-rotate(0deg);
    }
}

/* Navigation Styles */
.navbar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 1rem 0;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.navbar-brand {
    display: flex;
    align-items: center;
    position: relative;
}

.navbar-brand .logo-display {
    margin-right: 1rem;
}

.navbar-title {
    font-size: 1.8rem;
    font-weight: 800;
    background: linear-gradient(45deg, #ED3637, #F79E62, #ED3637);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
    letter-spacing: -0.5px;
    transition: all 0.3s ease;
}

.navbar-title::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(45deg, #ED3637, #F79E62);
    transition: width 0.3s ease;
}

.navbar-brand:hover .navbar-title::after {
    width: 100%;
}

.navbar-brand:hover .navbar-title {
    transform: translateY(-1px);
    text-shadow: 0 2px 8px rgba(237, 54, 55, 0.3);
}

.navbar-nav {
    display: flex;
    gap: 2rem;
    align-items: center;
}

.nav-link {
    color: #333;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: #ED3637;
    background: rgba(237, 54, 55, 0.1);
}

.navbar-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.navbar-toggle span {
    width: 25px;
    height: 3px;
    background: #333;
    transition: all 0.3s ease;
}

/* Mobile Navigation */
@media screen and (max-width: 768px) {
    .navbar-menu {
        position: fixed;
        top: 80px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 80px);
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(10px);
        transition: left 0.3s ease;
        padding: 2rem;
    }

    .navbar-menu.active {
        left: 0;
    }

    .navbar-nav {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .nav-link {
        padding: 1rem;
        text-align: center;
        border: 1px solid #f0f0f0;
    }

    .navbar-toggle {
        display: flex;
    }

    .navbar-toggle.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }

    .navbar-toggle.active span:nth-child(2) {
        opacity: 0;
    }

    .navbar-toggle.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 8rem 0 4rem;
    min-height: 80vh;
    display: flex;
    align-items: center;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
}

.hero-content {
    max-width: 500px;
}

.hero-title {
    font-size: 3rem;
    font-weight: bold;
    margin-bottom: 1.5rem;
    color: #333;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.2rem;
    color: #666;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
}

/* Features Section */
.features {
    padding: 5rem 0;
    background: white;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.feature-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 1px solid #f0f0f0;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.feature-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    background: linear-gradient(45deg, #ED3637, #F79E62);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    font-weight: bold;
}

.feature-card h3 {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 1rem;
    color: #333;
}

.feature-card p {
    color: #666;
    line-height: 1.6;
}

/* About Preview Section */
.about-preview {
    padding: 5rem 0;
    background: #f8f9fa;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
}

.about-text h2 {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 1.5rem;
    color: #333;
}

.about-text p {
    font-size: 1.1rem;
    color: #666;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.about-image {
    display: flex;
    justify-content: center;
}

.image-placeholder {
    width: 100%;
    height: 300px;
    background: linear-gradient(45deg, #ED3637, #F79E62);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    font-weight: bold;
}

/* CTA Section */
.cta {
    padding: 5rem 0;
    background: linear-gradient(45deg, #ED3637, #F79E62);
    color: white;
    text-align: center;
}

.cta-content h2 {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 1rem;
}

.cta-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.cta .btn-primary {
    background: white;
    color: #ED3637;
}

.cta .btn-primary:hover {
    background: #f8f9fa;
    transform: translateY(-2px);
}

/* About Page Styles */
.about-hero, .contact-hero {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 8rem 0 4rem;
    text-align: center;
}

.about-hero-content h1, .contact-hero-content h1 {
    font-size: 3.5rem;
    font-weight: bold;
    margin-bottom: 1rem;
    color: #333;
}

.about-hero-content p, .contact-hero-content p {
    font-size: 1.3rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

/* Company Story */
.company-story {
    padding: 5rem 0;
    background: white;
}

.story-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.story-text h2 {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 2rem;
    color: #333;
}

.story-text p {
    font-size: 1.1rem;
    color: #666;
    line-height: 1.8;
    margin-bottom: 1.5rem;
}

.story-image {
    display: flex;
    justify-content: center;
}

.story-image .image-placeholder {
    height: 400px;
}

/* Mission & Vision */
.mission-vision {
    padding: 5rem 0;
    background: #f8f9fa;
}

.mv-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    margin-top: 2rem;
}

.mv-card {
    background: white;
    padding: 3rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.mv-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.mv-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 2rem;
    background: linear-gradient(45deg, #ED3637, #F79E62);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    font-weight: bold;
}

.mv-card h3 {
    font-size: 1.8rem;
    font-weight: bold;
    margin-bottom: 1.5rem;
    color: #333;
}

.mv-card p {
    font-size: 1.1rem;
    color: #666;
    line-height: 1.6;
}

/* Values Section */
.values {
    padding: 5rem 0;
    background: white;
}

.values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.value-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 1px solid #f0f0f0;
}

.value-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
}

.value-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto 1.5rem;
    background: linear-gradient(45deg, #ED3637, #F79E62);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: bold;
    color: white;
}

.value-card h4 {
    font-size: 1.3rem;
    font-weight: bold;
    margin-bottom: 1rem;
    color: #333;
}

.value-card p {
    color: #666;
    line-height: 1.6;
}

/* Team Section */
.team {
    padding: 5rem 0;
    background: #f8f9fa;
    text-align: center;
}

.team-description {
    font-size: 1.2rem;
    color: #666;
    max-width: 800px;
    margin: 0 auto 3rem;
    line-height: 1.6;
}

.team-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.stat-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-card h3 {
    font-size: 3rem;
    font-weight: bold;
    background: linear-gradient(45deg, #ED3637, #F79E62);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.5rem;
}

.stat-card p {
    font-size: 1.1rem;
    color: #666;
    font-weight: 500;
}

/* Contact Page Styles */
.contact-content {
    padding: 5rem 0;
    background: white;
}

.contact-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
}

/* Contact Information */
.contact-info h2 {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 1.5rem;
    color: #333;
}

.contact-info > p {
    font-size: 1.1rem;
    color: #666;
    line-height: 1.6;
    margin-bottom: 3rem;
}

.contact-details {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.contact-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(45deg, #ED3637, #F79E62);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.contact-text h4 {
    font-size: 1.2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
    color: #333;
}

.contact-text p {
    color: #666;
    line-height: 1.6;
}

/* Contact Form */
.contact-form {
    background: #f8f9fa;
    padding: 3rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.contact-form h2 {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 2rem;
    color: #333;
    text-align: center;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 1rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
    box-sizing: border-box;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #ED3637;
    box-shadow: 0 0 0 3px rgba(237, 54, 55, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.contact-form .btn {
    width: 100%;
}

/* Map Section */
.map-section {
    padding: 5rem 0;
    background: #f8f9fa;
}

.map-placeholder {
    background: white;
    height: 400px;
    border-radius: 15px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.map-placeholder p {
    font-size: 1.2rem;
    color: #666;
    margin: 0.5rem 0;
}

.map-placeholder p:first-child {
    font-weight: bold;
    color: #333;
}

/* Footer Styles */
.footer {
    background: #333;
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h4 {
    font-size: 1.2rem;
    margin-bottom: 1rem;
    color: #F79E62;
}

.footer-section p,
.footer-section li {
    color: #ccc;
    line-height: 1.6;
    margin-bottom: 0.5rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section a {
    color: #ccc;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: #F79E62;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.footer-logo span {
    font-size: 1.5rem;
    font-weight: bold;
    background: linear-gradient(45deg, #ED3637, #F79E62);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.footer-bottom {
    border-top: 1px solid #555;
    padding-top: 1rem;
    text-align: center;
    color: #ccc;
}

/* Responsive Design */
@media screen and (max-width: 768px) {
    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
        padding-top: 2rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .about-content,
    .story-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .mv-grid {
        grid-template-columns: 1fr;
    }

    .values-grid {
        grid-template-columns: 1fr;
    }

    .team-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .contact-grid {
        grid-template-columns: 1fr;
        gap: 3rem;
    }

    .contact-form {
        padding: 2rem;
    }

    .contact-item {
        flex-direction: column;
        text-align: center;
        align-items: center;
    }

    .contact-icon {
        margin-bottom: 1rem;
    }

    .about-hero-content h1,
    .contact-hero-content h1 {
        font-size: 2.5rem;
    }

    .story-text h2,
    .contact-info h2,
    .contact-form h2 {
        font-size: 2rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .hero-buttons {
        justify-content: center;
    }

    .navbar-title {
        font-size: 1.5rem;
    }
}

@media screen and (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .about-hero-content h1,
    .contact-hero-content h1 {
        font-size: 2rem;
    }

    .about-hero-content p,
    .contact-hero-content p {
        font-size: 1.1rem;
    }

    .team-stats {
        grid-template-columns: 1fr;
    }

    .mv-card {
        padding: 2rem;
    }

    .contact-form {
        padding: 1.5rem;
    }

    .story-text h2,
    .contact-info h2,
    .contact-form h2 {
        font-size: 1.8rem;
    }

    .section-title {
        font-size: 1.8rem;
    }

    .map-placeholder {
        height: 300px;
    }

    .map-placeholder p {
        font-size: 1rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 100%;
        max-width: 300px;
    }

    .navbar-title {
        font-size: 1.4rem;
    }

    .logo-display.large .trendy-logo {
        width: 100px;
        height: 100px;
    }

    .logo-display.extra-large .trendy-logo {
        width: 150px;
        height: 150px;
    }
}

@media screen and (max-width: 320px) {
    .logo-display.large .trendy-logo {
        width: 80px;
        height: 80px;
    }

    .logo-display.extra-large .trendy-logo {
        width: 120px;
        height: 120px;
    }
}

/* Special Trendy Effects */
.logo-display.special-effect {
    animation: glitch 5s ease-in-out infinite;
}

.logo-display.neon-effect .logo-container {
    box-shadow:
        0 0 5px #FF6B6B,
        0 0 10px #FF6B6B,
        0 0 15px #FF6B6B,
        0 0 20px #4ECDC4,
        0 0 35px #4ECDC4,
        0 0 40px #45B7D1,
        inset 0 0 10px rgba(255, 255, 255, 0.1);
    animation: neonPulse 2s ease-in-out infinite alternate;
}

.logo-display.holographic .logo-container {
    background: linear-gradient(45deg,
        #ff0080, #ff8c00, #40e0d0, #ff0080,
        #9400d3, #00ff00, #ff1493, #00bfff);
    background-size: 800% 800%;
    animation: holographicShift 3s ease infinite;
}

.logo-display.cyberpunk .logo-container::before {
    background: linear-gradient(45deg,
        #00ff41, #ff0080, #00bfff, #ff4500,
        #9400d3, #00ff41);
    background-size: 600% 600%;
    animation: cyberpunkGlow 2s ease infinite;
    filter: blur(3px);
}

.logo-display.retro-wave .logo-container {
    background: linear-gradient(135deg,
        #ff006e, #8338ec, #3a86ff, #06ffa5,
        #ffbe0b, #ff006e);
    background-size: 500% 500%;
    animation: retroWave 4s ease infinite;
    border: 2px solid #ff006e;
}

/* Additional Keyframes for Special Effects */
@keyframes neonPulse {
    0% {
        box-shadow:
            0 0 5px #FF6B6B,
            0 0 10px #FF6B6B,
            0 0 15px #FF6B6B,
            0 0 20px #4ECDC4,
            0 0 35px #4ECDC4,
            0 0 40px #45B7D1;
    }
    100% {
        box-shadow:
            0 0 10px #FF6B6B,
            0 0 20px #FF6B6B,
            0 0 30px #FF6B6B,
            0 0 40px #4ECDC4,
            0 0 70px #4ECDC4,
            0 0 80px #45B7D1;
    }
}

@keyframes holographicShift {
    0% {
        background-position: 0% 50%;
        filter: hue-rotate(0deg);
    }
    50% {
        background-position: 100% 50%;
        filter: hue-rotate(180deg);
    }
    100% {
        background-position: 0% 50%;
        filter: hue-rotate(360deg);
    }
}

@keyframes cyberpunkGlow {
    0%, 100% {
        background-position: 0% 50%;
        opacity: 0.8;
    }
    50% {
        background-position: 100% 50%;
        opacity: 1;
    }
}

@keyframes retroWave {
    0% {
        background-position: 0% 50%;
        transform: scale(1);
    }
    25% {
        background-position: 25% 75%;
        transform: scale(1.02);
    }
    50% {
        background-position: 100% 50%;
        transform: scale(1);
    }
    75% {
        background-position: 75% 25%;
        transform: scale(1.02);
    }
    100% {
        background-position: 0% 50%;
        transform: scale(1);
    }
}
