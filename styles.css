/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #ffffff;
}

html {
    scroll-behavior: smooth;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Global Button Styles */
.btn {
    padding: 1rem 2rem;
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: linear-gradient(45deg, #ED3637, #F79E62);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(237, 54, 55, 0.3);
}

.btn-secondary {
    background: transparent;
    color: #ED3637;
    border: 2px solid #ED3637;
}

.btn-secondary:hover {
    background: #ED3637;
    color: white;
    transform: translateY(-2px);
}

/* Global Gradient Text */
.gradient-text {
    background: linear-gradient(45deg, #ED3637, #F79E62);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Global Section Title */
.section-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 3rem;
    color: #333;
}

/* Remove default link styles */
a {
    text-decoration: none;
    color: inherit;
}

/* Global responsive images */
img {
    max-width: 100%;
    height: auto;
}

/* Global list styles */
ul, ol {
    list-style: none;
}

/* Focus styles for accessibility */
*:focus {
    outline: 2px solid #ED3637;
    outline-offset: 2px;
}

/* Trendy Logo Display */
.logo-display {
    display: inline-block;
    position: relative;
}

.logo-container {
    position: relative;
    display: inline-block;
    border-radius: 50%;
    overflow: hidden;
}

.trendy-logo {
    display: block;
    border-radius: 50%;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    z-index: 2;
}

/* Size variations */
.logo-display.small .trendy-logo {
    width: 40px;
    height: 40px;
}

.logo-display .trendy-logo {
    width: 60px;
    height: 60px;
}

.logo-display.large .trendy-logo {
    width: 120px;
    height: 120px;
}

.logo-display.extra-large .trendy-logo {
    width: 200px;
    height: 200px;
}

/* Gradient border effect */
.logo-container::before {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    background: linear-gradient(45deg, #ED3637, #F79E62, #ED3637, #F79E62);
    border-radius: 50%;
    z-index: 1;
    animation: rotate 3s linear infinite;
}

.logo-container::after {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    background: white;
    border-radius: 50%;
    z-index: 1;
}

/* Overlay effects */
.logo-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 50%;
    z-index: 3;
    pointer-events: none;
}

.logo-shine {
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.3) 50%, transparent 70%);
    transform: rotate(45deg);
    transition: all 0.6s ease;
    opacity: 0;
}

/* Hover effects */
.logo-display:hover .trendy-logo {
    transform: scale(1.1) rotate(5deg);
    filter: brightness(1.1) contrast(1.1);
}

.logo-display:hover .logo-shine {
    opacity: 1;
    animation: shine 0.6s ease-in-out;
}

.logo-display:hover .logo-container::before {
    animation-duration: 1s;
}

/* Glow effect */
.logo-display.glow .logo-container {
    box-shadow: 0 0 20px rgba(237, 54, 55, 0.4),
                0 0 40px rgba(237, 54, 55, 0.2),
                0 0 60px rgba(237, 54, 55, 0.1);
}

.logo-display.glow:hover .logo-container {
    box-shadow: 0 0 30px rgba(237, 54, 55, 0.6),
                0 0 60px rgba(237, 54, 55, 0.4),
                0 0 90px rgba(237, 54, 55, 0.2);
}

/* Animated version */
.logo-display.animated .trendy-logo {
    animation: pulse 2s ease-in-out infinite;
}

.logo-display.animated .logo-container::before {
    animation: rotate 4s linear infinite, pulse-border 2s ease-in-out infinite;
}

/* Keyframe animations */
@keyframes rotate {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes shine {
    0% {
        transform: translateX(-100%) translateY(-100%) rotate(45deg);
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        transform: translateX(100%) translateY(100%) rotate(45deg);
        opacity: 0;
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes pulse-border {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

/* Navigation Styles */
.navbar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 1rem 0;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.navbar-brand {
    display: flex;
    align-items: center;
    position: relative;
}

.navbar-brand .logo-display {
    margin-right: 1rem;
}

.navbar-title {
    font-size: 1.8rem;
    font-weight: 800;
    background: linear-gradient(45deg, #ED3637, #F79E62, #ED3637);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
    letter-spacing: -0.5px;
    transition: all 0.3s ease;
}

.navbar-title::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(45deg, #ED3637, #F79E62);
    transition: width 0.3s ease;
}

.navbar-brand:hover .navbar-title::after {
    width: 100%;
}

.navbar-brand:hover .navbar-title {
    transform: translateY(-1px);
    text-shadow: 0 2px 8px rgba(237, 54, 55, 0.3);
}

.navbar-nav {
    display: flex;
    gap: 2rem;
    align-items: center;
}

.nav-link {
    color: #333;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: #ED3637;
    background: rgba(237, 54, 55, 0.1);
}

.navbar-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.navbar-toggle span {
    width: 25px;
    height: 3px;
    background: #333;
    transition: all 0.3s ease;
}

/* Mobile Navigation */
@media screen and (max-width: 768px) {
    .navbar-menu {
        position: fixed;
        top: 80px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 80px);
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(10px);
        transition: left 0.3s ease;
        padding: 2rem;
    }

    .navbar-menu.active {
        left: 0;
    }

    .navbar-nav {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .nav-link {
        padding: 1rem;
        text-align: center;
        border: 1px solid #f0f0f0;
    }

    .navbar-toggle {
        display: flex;
    }

    .navbar-toggle.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }

    .navbar-toggle.active span:nth-child(2) {
        opacity: 0;
    }

    .navbar-toggle.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 8rem 0 4rem;
    min-height: 80vh;
    display: flex;
    align-items: center;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
}

.hero-content {
    max-width: 500px;
}

.hero-title {
    font-size: 3rem;
    font-weight: bold;
    margin-bottom: 1.5rem;
    color: #333;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.2rem;
    color: #666;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
}

/* Features Section */
.features {
    padding: 5rem 0;
    background: white;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.feature-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 1px solid #f0f0f0;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.feature-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    background: linear-gradient(45deg, #ED3637, #F79E62);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    font-weight: bold;
}

.feature-card h3 {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 1rem;
    color: #333;
}

.feature-card p {
    color: #666;
    line-height: 1.6;
}

/* About Preview Section */
.about-preview {
    padding: 5rem 0;
    background: #f8f9fa;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
}

.about-text h2 {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 1.5rem;
    color: #333;
}

.about-text p {
    font-size: 1.1rem;
    color: #666;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.about-image {
    display: flex;
    justify-content: center;
}

.image-placeholder {
    width: 100%;
    height: 300px;
    background: linear-gradient(45deg, #ED3637, #F79E62);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    font-weight: bold;
}

/* CTA Section */
.cta {
    padding: 5rem 0;
    background: linear-gradient(45deg, #ED3637, #F79E62);
    color: white;
    text-align: center;
}

.cta-content h2 {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 1rem;
}

.cta-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.cta .btn-primary {
    background: white;
    color: #ED3637;
}

.cta .btn-primary:hover {
    background: #f8f9fa;
    transform: translateY(-2px);
}

/* About Page Styles */
.about-hero, .contact-hero {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 8rem 0 4rem;
    text-align: center;
}

.about-hero-content h1, .contact-hero-content h1 {
    font-size: 3.5rem;
    font-weight: bold;
    margin-bottom: 1rem;
    color: #333;
}

.about-hero-content p, .contact-hero-content p {
    font-size: 1.3rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

/* Company Story */
.company-story {
    padding: 5rem 0;
    background: white;
}

.story-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.story-text h2 {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 2rem;
    color: #333;
}

.story-text p {
    font-size: 1.1rem;
    color: #666;
    line-height: 1.8;
    margin-bottom: 1.5rem;
}

.story-image {
    display: flex;
    justify-content: center;
}

.story-image .image-placeholder {
    height: 400px;
}

/* Mission & Vision */
.mission-vision {
    padding: 5rem 0;
    background: #f8f9fa;
}

.mv-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    margin-top: 2rem;
}

.mv-card {
    background: white;
    padding: 3rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.mv-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.mv-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 2rem;
    background: linear-gradient(45deg, #ED3637, #F79E62);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    font-weight: bold;
}

.mv-card h3 {
    font-size: 1.8rem;
    font-weight: bold;
    margin-bottom: 1.5rem;
    color: #333;
}

.mv-card p {
    font-size: 1.1rem;
    color: #666;
    line-height: 1.6;
}

/* Values Section */
.values {
    padding: 5rem 0;
    background: white;
}

.values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.value-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 1px solid #f0f0f0;
}

.value-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
}

.value-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto 1.5rem;
    background: linear-gradient(45deg, #ED3637, #F79E62);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: bold;
    color: white;
}

.value-card h4 {
    font-size: 1.3rem;
    font-weight: bold;
    margin-bottom: 1rem;
    color: #333;
}

.value-card p {
    color: #666;
    line-height: 1.6;
}

/* Team Section */
.team {
    padding: 5rem 0;
    background: #f8f9fa;
    text-align: center;
}

.team-description {
    font-size: 1.2rem;
    color: #666;
    max-width: 800px;
    margin: 0 auto 3rem;
    line-height: 1.6;
}

.team-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.stat-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-card h3 {
    font-size: 3rem;
    font-weight: bold;
    background: linear-gradient(45deg, #ED3637, #F79E62);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.5rem;
}

.stat-card p {
    font-size: 1.1rem;
    color: #666;
    font-weight: 500;
}

/* Contact Page Styles */
.contact-content {
    padding: 5rem 0;
    background: white;
}

.contact-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
}

/* Contact Information */
.contact-info h2 {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 1.5rem;
    color: #333;
}

.contact-info > p {
    font-size: 1.1rem;
    color: #666;
    line-height: 1.6;
    margin-bottom: 3rem;
}

.contact-details {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.contact-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(45deg, #ED3637, #F79E62);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.contact-text h4 {
    font-size: 1.2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
    color: #333;
}

.contact-text p {
    color: #666;
    line-height: 1.6;
}

/* Contact Form */
.contact-form {
    background: #f8f9fa;
    padding: 3rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.contact-form h2 {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 2rem;
    color: #333;
    text-align: center;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 1rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
    box-sizing: border-box;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #ED3637;
    box-shadow: 0 0 0 3px rgba(237, 54, 55, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.contact-form .btn {
    width: 100%;
}

/* Map Section */
.map-section {
    padding: 5rem 0;
    background: #f8f9fa;
}

.map-placeholder {
    background: white;
    height: 400px;
    border-radius: 15px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.map-placeholder p {
    font-size: 1.2rem;
    color: #666;
    margin: 0.5rem 0;
}

.map-placeholder p:first-child {
    font-weight: bold;
    color: #333;
}

/* Footer Styles */
.footer {
    background: #333;
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h4 {
    font-size: 1.2rem;
    margin-bottom: 1rem;
    color: #F79E62;
}

.footer-section p,
.footer-section li {
    color: #ccc;
    line-height: 1.6;
    margin-bottom: 0.5rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section a {
    color: #ccc;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: #F79E62;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.footer-logo span {
    font-size: 1.5rem;
    font-weight: bold;
    background: linear-gradient(45deg, #ED3637, #F79E62);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.footer-bottom {
    border-top: 1px solid #555;
    padding-top: 1rem;
    text-align: center;
    color: #ccc;
}

/* Responsive Design */
@media screen and (max-width: 768px) {
    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
        padding-top: 2rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .about-content,
    .story-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .mv-grid {
        grid-template-columns: 1fr;
    }

    .values-grid {
        grid-template-columns: 1fr;
    }

    .team-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .contact-grid {
        grid-template-columns: 1fr;
        gap: 3rem;
    }

    .contact-form {
        padding: 2rem;
    }

    .contact-item {
        flex-direction: column;
        text-align: center;
        align-items: center;
    }

    .contact-icon {
        margin-bottom: 1rem;
    }

    .about-hero-content h1,
    .contact-hero-content h1 {
        font-size: 2.5rem;
    }

    .story-text h2,
    .contact-info h2,
    .contact-form h2 {
        font-size: 2rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .hero-buttons {
        justify-content: center;
    }

    .navbar-title {
        font-size: 1.5rem;
    }
}

@media screen and (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .about-hero-content h1,
    .contact-hero-content h1 {
        font-size: 2rem;
    }

    .about-hero-content p,
    .contact-hero-content p {
        font-size: 1.1rem;
    }

    .team-stats {
        grid-template-columns: 1fr;
    }

    .mv-card {
        padding: 2rem;
    }

    .contact-form {
        padding: 1.5rem;
    }

    .story-text h2,
    .contact-info h2,
    .contact-form h2 {
        font-size: 1.8rem;
    }

    .section-title {
        font-size: 1.8rem;
    }

    .map-placeholder {
        height: 300px;
    }

    .map-placeholder p {
        font-size: 1rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 100%;
        max-width: 300px;
    }

    .navbar-title {
        font-size: 1.4rem;
    }

    .logo-display.large .trendy-logo {
        width: 100px;
        height: 100px;
    }

    .logo-display.extra-large .trendy-logo {
        width: 150px;
        height: 150px;
    }
}

@media screen and (max-width: 320px) {
    .logo-display.large .trendy-logo {
        width: 80px;
        height: 80px;
    }

    .logo-display.extra-large .trendy-logo {
        width: 120px;
        height: 120px;
    }
}
