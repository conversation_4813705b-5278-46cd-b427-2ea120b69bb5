{"ast": null, "code": "var _jsxFileName = \"C:\\\\arihant_sales\\\\arihant-sales\\\\src\\\\components\\\\Navbar.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link } from 'react-router-dom';\nimport './Navbar.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Navbar = () => {\n  _s();\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const toggleMenu = () => {\n    setIsMenuOpen(!isMenuOpen);\n  };\n  return /*#__PURE__*/_jsxDEV(\"nav\", {\n    className: \"navbar\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"navbar-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"navbar-brand\",\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"/logo.jpg\",\n            alt: \"Arihant Sales\",\n            className: \"navbar-logo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 17,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"navbar-title\",\n            children: \"Arihant Sales\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 18,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `navbar-menu ${isMenuOpen ? 'active' : ''}`,\n        children: /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"navbar-nav\",\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"nav-item\",\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/\",\n              className: \"nav-link\",\n              onClick: () => setIsMenuOpen(false),\n              children: \"Home\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 25,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"nav-item\",\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/about\",\n              className: \"nav-link\",\n              onClick: () => setIsMenuOpen(false),\n              children: \"About\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 30,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"nav-item\",\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/contact\",\n              className: \"nav-link\",\n              onClick: () => setIsMenuOpen(false),\n              children: \"Contact Us\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 35,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"navbar-toggle\",\n        onClick: toggleMenu,\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"bar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"bar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"bar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 13,\n    columnNumber: 5\n  }, this);\n};\n_s(Navbar, \"vK10R+uCyHfZ4DZVnxbYkMWJB8g=\");\n_c = Navbar;\nexport default Navbar;\nvar _c;\n$RefreshReg$(_c, \"Navbar\");", "map": {"version": 3, "names": ["React", "useState", "Link", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON>", "_s", "isMenuOpen", "setIsMenuOpen", "toggleMenu", "className", "children", "to", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/arihant_sales/arihant-sales/src/components/Navbar.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link } from 'react-router-dom';\nimport './Navbar.css';\n\nconst Navbar = () => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n\n  const toggleMenu = () => {\n    setIsMenuOpen(!isMenuOpen);\n  };\n\n  return (\n    <nav className=\"navbar\">\n      <div className=\"navbar-container\">\n        <div className=\"navbar-brand\">\n          <Link to=\"/\">\n            <img src=\"/logo.jpg\" alt=\"Arihant Sales\" className=\"navbar-logo\" />\n            <span className=\"navbar-title\">Arihant Sales</span>\n          </Link>\n        </div>\n        \n        <div className={`navbar-menu ${isMenuOpen ? 'active' : ''}`}>\n          <ul className=\"navbar-nav\">\n            <li className=\"nav-item\">\n              <Link to=\"/\" className=\"nav-link\" onClick={() => setIsMenuOpen(false)}>\n                Home\n              </Link>\n            </li>\n            <li className=\"nav-item\">\n              <Link to=\"/about\" className=\"nav-link\" onClick={() => setIsMenuOpen(false)}>\n                About\n              </Link>\n            </li>\n            <li className=\"nav-item\">\n              <Link to=\"/contact\" className=\"nav-link\" onClick={() => setIsMenuOpen(false)}>\n                Contact Us\n              </Link>\n            </li>\n          </ul>\n        </div>\n\n        <div className=\"navbar-toggle\" onClick={toggleMenu}>\n          <span className=\"bar\"></span>\n          <span className=\"bar\"></span>\n          <span className=\"bar\"></span>\n        </div>\n      </div>\n    </nav>\n  );\n};\n\nexport default Navbar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAO,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtB,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGP,QAAQ,CAAC,KAAK,CAAC;EAEnD,MAAMQ,UAAU,GAAGA,CAAA,KAAM;IACvBD,aAAa,CAAC,CAACD,UAAU,CAAC;EAC5B,CAAC;EAED,oBACEH,OAAA;IAAKM,SAAS,EAAC,QAAQ;IAAAC,QAAA,eACrBP,OAAA;MAAKM,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BP,OAAA;QAAKM,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC3BP,OAAA,CAACF,IAAI;UAACU,EAAE,EAAC,GAAG;UAAAD,QAAA,gBACVP,OAAA;YAAKS,GAAG,EAAC,WAAW;YAACC,GAAG,EAAC,eAAe;YAACJ,SAAS,EAAC;UAAa;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnEd,OAAA;YAAMM,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAa;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENd,OAAA;QAAKM,SAAS,EAAE,eAAeH,UAAU,GAAG,QAAQ,GAAG,EAAE,EAAG;QAAAI,QAAA,eAC1DP,OAAA;UAAIM,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACxBP,OAAA;YAAIM,SAAS,EAAC,UAAU;YAAAC,QAAA,eACtBP,OAAA,CAACF,IAAI;cAACU,EAAE,EAAC,GAAG;cAACF,SAAS,EAAC,UAAU;cAACS,OAAO,EAAEA,CAAA,KAAMX,aAAa,CAAC,KAAK,CAAE;cAAAG,QAAA,EAAC;YAEvE;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACLd,OAAA;YAAIM,SAAS,EAAC,UAAU;YAAAC,QAAA,eACtBP,OAAA,CAACF,IAAI;cAACU,EAAE,EAAC,QAAQ;cAACF,SAAS,EAAC,UAAU;cAACS,OAAO,EAAEA,CAAA,KAAMX,aAAa,CAAC,KAAK,CAAE;cAAAG,QAAA,EAAC;YAE5E;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACLd,OAAA;YAAIM,SAAS,EAAC,UAAU;YAAAC,QAAA,eACtBP,OAAA,CAACF,IAAI;cAACU,EAAE,EAAC,UAAU;cAACF,SAAS,EAAC,UAAU;cAACS,OAAO,EAAEA,CAAA,KAAMX,aAAa,CAAC,KAAK,CAAE;cAAAG,QAAA,EAAC;YAE9E;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAENd,OAAA;QAAKM,SAAS,EAAC,eAAe;QAACS,OAAO,EAAEV,UAAW;QAAAE,QAAA,gBACjDP,OAAA;UAAMM,SAAS,EAAC;QAAK;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC7Bd,OAAA;UAAMM,SAAS,EAAC;QAAK;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC7Bd,OAAA;UAAMM,SAAS,EAAC;QAAK;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACZ,EAAA,CA7CID,MAAM;AAAAe,EAAA,GAANf,MAAM;AA+CZ,eAAeA,MAAM;AAAC,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}