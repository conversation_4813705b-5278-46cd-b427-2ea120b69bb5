/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #ffffff;
}

.App {
  min-height: 100vh;
}

/* Global Button Styles */
.btn {
  padding: 1rem 2rem;
  border: none;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.btn-primary {
  background: linear-gradient(45deg, #ED3637, #F79E62);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(237, 54, 55, 0.3);
}

.btn-secondary {
  background: transparent;
  color: #ED3637;
  border: 2px solid #ED3637;
}

.btn-secondary:hover {
  background: #ED3637;
  color: white;
  transform: translateY(-2px);
}

/* Global Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Global Gradient Text */
.gradient-text {
  background: linear-gradient(45deg, #ED3637, #F79E62);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Global Section Title */
.section-title {
  text-align: center;
  font-size: 2.5rem;
  font-weight: bold;
  margin-bottom: 3rem;
  color: #333;
}

/* Smooth Scrolling */
html {
  scroll-behavior: smooth;
}

/* Remove default link styles */
a {
  text-decoration: none;
  color: inherit;
}

/* Global responsive images */
img {
  max-width: 100%;
  height: auto;
}

/* Global list styles */
ul, ol {
  list-style: none;
}

/* Focus styles for accessibility */
*:focus {
  outline: 2px solid #ED3637;
  outline-offset: 2px;
}
